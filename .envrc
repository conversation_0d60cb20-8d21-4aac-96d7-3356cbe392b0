# Clear existing aliases when entering a directory
rm -rf "$PWD/.envrc-aliases"

export_alias() {
  # Create a new alias
  local name=$1
  shift

  local alias_dir="$PWD/.envrc-aliases"
  local alias_file="$alias_dir/$name"

  # If this is the first time we're calling export_alias, add to PATH once
  if ! [[ ":$PATH:" == *":$alias_dir:"* ]]; then
    mkdir -p "$alias_dir"
    PATH_add "$alias_dir"
  fi

  # Write the alias file
  cat <<EOT >"$alias_file"
#!/usr/bin/env bash
set -e
exec $@ "\$@"
EOT
  chmod +x "$alias_file"
}

export_alias 'php' './vendor/bin/sail php'
export_alias 'composer' './vendor/bin/sail composer'
export_alias 'pa' './vendor/bin/sail php artisan'
export_alias 'npm' './vendor/bin/sail npm'
export_alias 'node' './vendor/bin/sail node'
export_alias 'npx' './vendor/bin/sail npx'
