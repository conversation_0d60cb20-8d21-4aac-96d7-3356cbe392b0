services:
    laravel.test:
        build:
            context: './vendor/laravel/sail/runtimes/8.3'
            dockerfile: Dockerfile
            args:
                WWWGROUP: '${WWWGROUP}'
        image: 'sail-8.3/app'
        extra_hosts:
            - 'host.docker.internal:host-gateway'
        ports:
            - '${APP_PORT:-80}:80'
            - '${VITE_PORT:-5173}:${VITE_PORT:-5173}'
        environment:
            WWWUSER: '${WWWUSER}'
            LARAVEL_SAIL: 1
            XDEBUG_MODE: '${SAIL_XDEBUG_MODE:-off}'
            XDEBUG_CONFIG: '${SAIL_XDEBUG_CONFIG:-client_host=host.docker.internal}'
            IGNITION_LOCAL_SITES_PATH: '${PWD}'
        volumes:
            - '.:/var/www/html'
        networks:
            - sail
        depends_on:
            - pgsql

    pgsql:
        image: 'postgres:17'
        ports:
            - '${FORWARD_DB_PORT:-5432}:5432'
        environment:
            PGPASSWORD: '${DB_PASSWORD:-secret}'
            POSTGRES_DB: '${DB_DATABASE}'
            POSTGRES_USER: '${DB_USERNAME}'
            POSTGRES_PASSWORD: '${DB_PASSWORD:-secret}'
        volumes:
            - 'sail-pgsql:/var/lib/postgresql/data'
            - './vendor/laravel/sail/database/pgsql/create-testing-database.sql:/docker-entrypoint-initdb.d/10-create-testing-database.sql'
        networks:
            - sail
        healthcheck:
            test:
                - CMD
                - pg_isready
                - '-q'
                - '-d'
                - '${DB_DATABASE}'
                - '-U'
                - '${DB_USERNAME}'
            retries: 3
            timeout: 5s

    storage:
        hostname: minio
        image: minio/minio:latest
        command: server /data --console-address :9001
        environment:
            MINIO_ROOT_USER: minio
            MINIO_ROOT_PASSWORD: minio123
            MINIO_BROWSER: "on"
            MINIO_DOMAIN: minio
        healthcheck:
            test:
              [
                  "CMD",
                  "curl",
                  "-f",
                  "http://localhost:9000/minio/health/live"
              ]
            interval: 30s
            timeout: 20s
            retries: 3
        volumes:
            - ./storage/data:/data
        ports:
            - ${MINIO_API_PORT:-9000}:9000
            - 9001:9001
        networks:
            - sail

    createbuckets:
        image: minio/mc
        container_name: 'minio_createbuckets'
        depends_on:
            - storage
        entrypoint: >
            /bin/sh -c '
            sleep 5;
            /usr/bin/mc config host add myminio http://minio:9000 minio minio123;
            /usr/bin/mc mb --ignore-existing myminio/default;
            /usr/bin/mc policy set public myminio/default;
            exit 0;
            '
        networks:
            - sail
networks:
    sail:
        driver: bridge
volumes:
    sail-pgsql:
        driver: local
