{"$schema": "https://getcomposer.org/schema.json", "name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.2", "dotswan/filament-map-picker": "^1.8", "filament/filament": "^3.3", "filament/spatie-laravel-media-library-plugin": "^3.3", "knuckleswtf/scribe": "^5.2", "laravel/framework": "^12.0", "laravel/sanctum": "^4.0", "laravel/tinker": "^2.10.1", "league/flysystem-aws-s3-v3": "^3.0", "spatie/laravel-permission": "^6.18"}, "require-dev": {"fakerphp/faker": "^1.23", "larastan/larastan": "^3.4", "laravel/pail": "^1.2.2", "laravel/pint": "^1.13", "laravel/sail": "^1.41", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.6", "pestphp/pest": "^3.8", "pestphp/pest-plugin-laravel": "^3.2", "pestphp/pest-plugin-livewire": "^3.0", "pestphp/pest-plugin-type-coverage": "^3.5", "projektgopher/whisky": "^0.7.4", "rector/rector": "^2.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi", "@php artisan filament:upgrade"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"], "dev": ["Composer\\Config::disableProcessTimeout", "npx concurrently -c \"#93c5fd,#c4b5fd,#fb7185,#fdba74\" \"php artisan serve\" \"php artisan queue:listen --tries=1\" \"php artisan pail --timeout=0\" \"npm run dev\" --names=server,queue,logs,vite"], "lint": "pint", "refactor": "rector", "phpstan": "vendor/bin/phpstan -c phpstan.neon", "test:type-coverage": "pest --type-coverage --compact --min=100 --memory-limit=2G", "test:lint": "pint --test", "test:unit": ["@putenv XDEBUG_MODE=coverage", "pest --parallel --coverage --exactly=100 --compact", "@putenv XDEBUG_MODE=off"], "test:types": "phpstan --memory-limit=2G", "test:refactor": "rector --dry-run", "test": ["@test:type-coverage", "@test:types", "@test:lint", "@test:refactor", "@test:unit"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}