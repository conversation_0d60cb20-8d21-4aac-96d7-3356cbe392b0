name: Resident
description: ''
endpoints:
  -
    httpMethods:
      - POST
    uri: api/resident/login
    metadata:
      groupName: Resident
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: ''
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      email:
        name: email
        description: 'The email of the resident'
        required: true
        example: <EMAIL>
        type: email
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      password:
        name: password
        description: 'The password of the resident'
        required: false
        example: '|]|{+-'
        type: required
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      device_name:
        name: device_name
        description: 'The name of the device'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      email: g<PERSON><EMAIL>
      password: '|]|{+-'
      device_name: architecto
    fileParameters: []
    responses:
      -
        status: 200
        content: json
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/resident/profile
    metadata:
      groupName: Resident
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: ''
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: json
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/resident/logout
    metadata:
      groupName: Resident
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: ''
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: json
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/resident/report/{id}'
    metadata:
      groupName: Resident
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: ''
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the report.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: json
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/resident/reports/send
    metadata:
      groupName: Resident
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: ''
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      title:
        name: title
        description: 'The title of the report'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      location:
        name: location
        description: 'The location of the report'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      content:
        name: content
        description: 'The content of the report'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      institution_branch_id:
        name: institution_branch_id
        description: 'The institution branch of the report'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      type_id:
        name: type_id
        description: 'The type of the report'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      category_id:
        name: category_id
        description: 'The category of the report'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      title: architecto
      location: architecto
      content: architecto
      institution_branch_id: architecto
      type_id: architecto
      category_id: architecto
    fileParameters: []
    responses:
      -
        status: 200
        content: json
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/resident/reports
    metadata:
      groupName: Resident
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: ''
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: json
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
