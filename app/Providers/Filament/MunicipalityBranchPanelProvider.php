<?php

declare(strict_types=1);

namespace App\Providers\Filament;

use App\Models\MunicipalityBranch;
use Filament\FontProviders\LocalFontProvider;
use Filament\Http\Middleware\Authenticate;
use Filament\Http\Middleware\AuthenticateSession;
use Filament\Http\Middleware\DisableBladeIconComponents;
use Filament\Http\Middleware\DispatchServingFilamentEvent;
use Filament\Pages;
use Filament\Panel;
use Filament\PanelProvider;
use Filament\Support\Colors\Color;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use Illuminate\Cookie\Middleware\EncryptCookies;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\View\Middleware\ShareErrorsFromSession;

class MunicipalityBranchPanelProvider extends PanelProvider
{
    public function panel(Panel $panel): Panel
    {
        return $panel
            ->id('municipalityBranch')
            ->path('municipalityBranch')
            ->tenant(MunicipalityBranch::class)
            ->login()
            ->colors([
                'primary' => Color::Amber,
            ])
            ->font(
                'sst-arabic',
                url: asset(asset('fonts/font.css')),
                provider: LocalFontProvider::class,
            )
            ->discoverResources(in: app_path('Filament/MunicipalityBranch/Resources'), for: 'App\\Filament\\MunicipalityBranch\\Resources')
            ->discoverPages(in: app_path('Filament/MunicipalityBranch/Pages'), for: 'App\\Filament\\MunicipalityBranch\\Pages')
            ->pages([
                Pages\Dashboard::class,
            ])
            ->discoverWidgets(in: app_path('Filament/MunicipalityBranche/Widgets'), for: 'App\\Filament\\MunicipalityBranche\\Widgets')
            ->widgets([

            ])
            ->middleware([
                EncryptCookies::class,
                AddQueuedCookiesToResponse::class,
                StartSession::class,
                AuthenticateSession::class,
                ShareErrorsFromSession::class,
                VerifyCsrfToken::class,
                SubstituteBindings::class,
                DisableBladeIconComponents::class,
                DispatchServingFilamentEvent::class,
            ])
            ->authMiddleware([
                Authenticate::class,
            ]);
    }
}
