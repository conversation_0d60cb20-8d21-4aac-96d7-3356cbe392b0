<?php

declare(strict_types=1);

namespace App\Models;

use Database\Factories\FAQFactory;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class FAQ extends Model
{
    /** @use HasFactory<FAQFactory> */
    use HasFactory;

    use HasUuids;

    protected $table = 'faqs';

    protected $fillable = [
        'question',
        'answer',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];
}
