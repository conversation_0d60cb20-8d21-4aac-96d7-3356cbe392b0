<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

/**
 * @param  $name  string
 * @param  $location  array
 * @param  $phone  string
 * @param  $status  string
 * @param  $email  string
 */
class MunicipalityBranch extends Model implements HasMedia
{
    use HasFactory, HasUuids, InteractsWithMedia;

    protected $fillable = [
        'municipality_id',
        'name',
        'location',
        'phone',
        'status',
        'email',
    ];

    protected $casts = [
        'location' => 'array',
    ];

    public function municipality(): BelongsTo
    {
        return $this->belongsTo(Municipality::class);
    }

    public function municipality_branches() {}

    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'municipality_branch_user');
    }

    public function institution_branches(): BelongsToMany
    {
        return $this->belongsToMany(InstitutionBranch::class, 'municipality_branch_institution_branch', 'municipality_branch_id', 'institution_branch_id');
    }
}
