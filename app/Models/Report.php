<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOneThrough;

class Report extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'title',
        'location',
        'content',
        'resident_id',
        'institution_branch_id',
        'type_id',
        'category_id',
    ];

    protected $casts = [
        'location' => 'array',
    ];

    public function resident(): BelongsTo
    {
        return $this->belongsTo(Resident::class);
    }

    public function institution_branch(): BelongsTo
    {
        return $this->belongsTo(InstitutionBranch::class);
    }

    public function institution(): HasOneThrough
    {
        /** @var HasOneThrough<Institution, InstitutionBranch, $this> */
        return $this->hasOneThrough(
            Institution::class,
            InstitutionBranch::class,
            'id',
            'id',
            'institution_branch_id',
            'institution_id'
        );
    }

    public function type(): BelongsTo
    {
        return $this->belongsTo(ReportType::class);
    }

    public function category(): BelongsTo
    {
        return $this->belongsTo(ReportCategory::class);
    }

    public function timelines(): HasMany
    {
        return $this->hasMany(ReportTimeline::class);
    }

    public function municipality(): HasOneThrough
    {
        /** @var HasOneThrough<Municipality, MunicipalityBranch, $this> */
        return $this->hasOneThrough(
            Municipality::class,
            MunicipalityBranch::class,
            'id',
            'id',
            'municipality_branch_id',
            'municipality_id'
        );
    }

    public function municipalityBranch(): BelongsTo
    {
        return $this->belongsTo(MunicipalityBranch::class);
    }

    public function institutionBranch(): BelongsTo
    {
        return $this->belongsTo(InstitutionBranch::class);
    }
}
