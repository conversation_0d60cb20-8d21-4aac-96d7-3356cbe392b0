<?php

declare(strict_types=1);

namespace App\Filament\Institution\Resources\InstitutionBranchResource\Pages;

use App\Filament\Institution\Resources\InstitutionBranchResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewInstitutionBranch extends ViewRecord
{
    protected static string $resource = InstitutionBranchResource::class;

    public static function getNavigationLabel(): string
    {
        return __('View');
    }

    public function getTitle(): string
    {
        return __('View');
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }
}
