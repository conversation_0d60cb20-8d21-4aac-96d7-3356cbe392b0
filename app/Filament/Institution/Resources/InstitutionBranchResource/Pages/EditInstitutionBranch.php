<?php

declare(strict_types=1);

namespace App\Filament\Institution\Resources\InstitutionBranchResource\Pages;

use App\Filament\Institution\Resources\InstitutionBranchResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditInstitutionBranch extends EditRecord
{
    protected static string $resource = InstitutionBranchResource::class;

    public static function getNavigationLabel(): string
    {
        return __('Edit Institution Branch');
    }

    public function getTitle(): string
    {
        return __('Edit Institution Branch');
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }
}
