<?php

declare(strict_types=1);

namespace App\Filament\Institution\Resources\InstitutionBranchResource\Pages;

use App\Filament\Institution\Resources\InstitutionBranchResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListInstitutionBranches extends ListRecords
{
    protected static string $resource = InstitutionBranchResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->label('Create Institution Branch')
                ->translateLabel(),
        ];
    }
}
