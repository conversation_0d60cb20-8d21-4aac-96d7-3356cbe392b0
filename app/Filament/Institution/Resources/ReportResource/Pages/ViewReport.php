<?php

declare(strict_types=1);

namespace App\Filament\Institution\Resources\ReportResource\Pages;

use App\Filament\Institution\Resources\ReportResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewReport extends ViewRecord
{
    protected static string $resource = ReportResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }
}
