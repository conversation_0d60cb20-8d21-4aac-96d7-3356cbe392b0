<?php

declare(strict_types=1);

namespace App\Filament\Institution\Resources;

use App\Filament\Institution\Resources\InstitutionBranchResource\Pages;
use App\Models\Institution;
use App\Models\InstitutionBranch;
use Dotswan\MapPicker\Fields\Map;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Pages\Page;
use Filament\Pages\SubNavigationPosition;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class InstitutionBranchResource extends Resource
{
    protected static ?string $model = InstitutionBranch::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static SubNavigationPosition $subNavigationPosition = SubNavigationPosition::Top;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Grid::make()
                    ->columnSpan(2)
                    ->schema([
                        Section::make(__('Branch Information'))
                            ->columns(1)
                            ->schema([
                                Select::make('institution_id')
                                    ->label('Institution')
                                    ->translateLabel()
                                    ->options(Institution::all()->pluck('name', 'id'))
                                    ->live()
                                    ->searchable()
                                    ->required(),
                                TextInput::make('name')
                                    ->label('Name')
                                    ->translateLabel()
                                    ->required()
                                    ->maxLength(255),
                                TextInput::make('phone')
                                    ->label('Phone')
                                    ->translateLabel()
                                    ->tel()
                                    ->maxLength(255),
                                Map::make('location')
                                    ->label('Location')
                                    ->translateLabel()
                                    ->defaultLocation(latitude: 32.3152506, longitude: 15.0148311)
                                    ->showMarker(true)
                                    ->clickable(true)
                                    ->tilesUrl('https://tile.openstreetmap.de/{z}/{x}/{y}.png')
                                    ->zoom(6),
                            ]),
                    ]),
                Grid::make()
                    ->columnSpan(1)
                    ->columns(1)
                    ->schema([
                        Section::make(__('Status'))
                            ->columns(1)
                            ->schema([
                                Select::make('status')
                                    ->label('Status')
                                    ->translateLabel()
                                    ->options([
                                        'active' => __('Active'),
                                        'inactive' => __('Inactive'),
                                    ])
                                    ->default('active')
                                    ->required(),
                            ]),
                    ]),
            ])->columns(3);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('Name')
                    ->translateLabel()
                    ->searchable(),
                Tables\Columns\TextColumn::make('phone')
                    ->label('Phone')
                    ->translateLabel()
                    ->searchable(),
                Tables\Columns\TextColumn::make('status')
                    ->label('Status')
                    ->translateLabel()
                    ->badge()
                    ->formatStateUsing(fn (string $state) => __($state))
                    ->color(fn (string $state): string => match ($state) {
                        'active' => 'success',
                        'inactive' => 'danger',
                        default => 'gray',
                    })
                    ->searchable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListInstitutionBranches::route('/'),
            'create' => Pages\CreateInstitutionBranch::route('/create'),
            'view' => Pages\ViewInstitutionBranch::route('/{record}'),
            'edit' => Pages\EditInstitutionBranch::route('/{record}/edit'),
            'users' => Pages\ManageUsers::route('/{record}/users'),
            'municipality-branches' => Pages\ManageMunicipalityBranches::route('/{record}/municipality-branches'),
        ];
    }

    public static function getRecordSubNavigation(Page $page): array
    {
        return $page->generateNavigationItems([
            Pages\EditInstitutionBranch::class,
            Pages\ManageUsers::class,
            Pages\ManageMunicipalityBranches::class,
        ]);
    }

    public static function getPluralModelLabel(): string
    {
        return __('Institution Branches');
    }

    public static function getNavigationLabel(): string
    {
        return __('Institution Branches');
    }

    public function getTitle(): string
    {
        return __('Institution Branches');
    }
}
