<?php

declare(strict_types=1);

namespace App\Filament\Institution\Resources\UserResource\Pages;

use App\Filament\Institution\Resources\UserResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewUser extends ViewRecord
{
    protected static string $resource = UserResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }
}
