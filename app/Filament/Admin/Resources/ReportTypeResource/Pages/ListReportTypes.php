<?php

declare(strict_types=1);

namespace App\Filament\Admin\Resources\ReportTypeResource\Pages;

use App\Filament\Admin\Resources\ReportTypeResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListReportTypes extends ListRecords
{
    protected static string $resource = ReportTypeResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->label('Create Report Type')
                ->translateLabel(),
        ];
    }
}
