<?php

declare(strict_types=1);

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\InstitutionBranchResource\Pages;
use App\Models\Institution;
use App\Models\InstitutionBranch;
use Dotswan\MapPicker\Fields\Map;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Pages\Page;
use Filament\Pages\SubNavigationPosition;
use Filament\Resources\Resource;
use Filament\Tables\Actions\BulkActionGroup;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\DeleteBulkAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;

class InstitutionBranchResource extends Resource
{
    protected static ?string $model = InstitutionBranch::class;

    protected static ?string $navigationIcon = 'heroicon-o-building-library';

    protected static ?int $navigationSort = 5;

    protected static SubNavigationPosition $subNavigationPosition = SubNavigationPosition::Top;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Grid::make()
                    ->columnSpan(2)
                    ->schema([
                        Section::make(__('Branch Information'))
                            ->columns(1)
                            ->schema([
                                Select::make('institution_id')
                                    ->label('Institution')
                                    ->translateLabel()
                                    ->options(Institution::all()->pluck('name', 'id'))
                                    ->live()
                                    ->searchable()
                                    ->required(),
                                TextInput::make('name')
                                    ->label('Name')
                                    ->translateLabel()
                                    ->required()
                                    ->maxLength(255),
                                TextInput::make('phone')
                                    ->label('Phone')
                                    ->translateLabel()
                                    ->tel()
                                    ->maxLength(255),
                                Map::make('location')
                                    ->label('Location')
                                    ->translateLabel()
                                    ->defaultLocation(latitude: 32.3152506, longitude: 15.0148311)
                                    ->showMarker(true)
                                    ->clickable(true)
                                    ->tilesUrl('https://tile.openstreetmap.de/{z}/{x}/{y}.png')
                                    ->zoom(6),
                            ]),
                    ]),
                Grid::make()
                    ->columnSpan(1)
                    ->columns(1)
                    ->schema([
                        Section::make(__('Status'))
                            ->columns(1)
                            ->schema([
                                Select::make('status')
                                    ->label('Status')
                                    ->translateLabel()
                                    ->options([
                                        'active' => __('Active'),
                                        'inactive' => __('Inactive'),
                                    ])
                                    ->default('active')
                                    ->required(),
                            ]),
                    ]),
            ])->columns(3);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('institution.name')
                    ->label('Institution')
                    ->translateLabel()
                    ->searchable()
                    ->sortable(),
                TextColumn::make('name')
                    ->label('Name')
                    ->translateLabel()
                    ->searchable()
                    ->sortable(),
                TextColumn::make('phone')
                    ->label('Phone')
                    ->translateLabel()
                    ->searchable(),
                TextColumn::make('location')
                    ->label('Location')
                    ->translateLabel()
                    ->searchable(),
                TextColumn::make('status')
                    ->label('Status')
                    ->translateLabel()
                    ->formatStateUsing(fn (string $state) => __($state))
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'active' => 'success',
                        'inactive' => 'danger',
                        default => 'gray',
                    }),
                TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('institution_id')
                    ->label('Institution')
                    ->translateLabel()
                    ->options(Institution::all()->pluck('name', 'id'))
                    ->searchable(),
                SelectFilter::make('status')
                    ->label('Status')
                    ->translateLabel()
                    ->options([
                        'active' => __('Active'),
                        'inactive' => __('Inactive'),
                    ]),
            ])
            ->actions([
                ViewAction::make(),
                EditAction::make(),
                DeleteAction::make(),
            ])
            ->bulkActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListInstitutionBranches::route('/'),
            'create' => Pages\CreateInstitutionBranch::route('/create'),
            'edit' => Pages\EditInstitutionBranch::route('/{record}/edit'),
            'view' => Pages\ViewInstitutionBranch::route('/{record}'),
            'users' => Pages\ManageUsers::route('/{record}/users'),
            'manage-municipality-branches' => Pages\ManageMunicipalityBranches::route('/{record}/municipality-branches'),
        ];
    }

    public static function getRecordSubNavigation(Page $page): array
    {
        return $page->generateNavigationItems([
            Pages\EditInstitutionBranch::class,
            Pages\ManageUsers::class,
            Pages\ManageMunicipalityBranches::class,
        ]);
    }

    public static function getPluralModelLabel(): string
    {
        return __('Institution Branches');
    }

    public static function getNavigationLabel(): string
    {
        return __('Institution Branches');
    }

    public static function getNavigationGroup(): ?string
    {
        return __('Tenants');
    }

    public function getTitle(): string
    {
        return __('Institution Branches');
    }
}
