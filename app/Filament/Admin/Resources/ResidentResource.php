<?php

declare(strict_types=1);

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\ResidentResource\Pages;
use App\Models\Municipality;
use App\Models\MunicipalityBranch;
use App\Models\Resident;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class ResidentResource extends Resource
{
    protected static ?string $model = Resident::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('')->schema([
                    TextInput::make('name')
                        ->label('Name')
                        ->translateLabel()
                        ->required()
                        ->maxLength(255),
                    TextInput::make('phone')
                        ->label('Phone')
                        ->translateLabel()
                        ->tel()
                        ->required()
                        ->maxLength(255),
                    TextInput::make('email')
                        ->label('Email')
                        ->translateLabel()
                        ->email()
                        ->required()
                        ->maxLength(255),
                    TextInput::make('password')
                        ->label('Password')
                        ->translateLabel()
                        ->password()
                        ->required()
                        ->maxLength(255),
                    /*Select::make('type')
                        ->label('Type')
                        ->translateLabel()
                        ->required()
                        ->options(['resident' => __('Resident'), 'citizen' => __('Citizen')]),*/
                    Select::make('status')
                        ->label('Status')
                        ->translateLabel()
                        ->required()
                        ->options(['active' => __('Active'), 'inactive' => __('Inactive')]),
                    Select::make('municipality_id')
                        ->options(Municipality::where('status', '=', 'active')->pluck('name', 'id'))
                        ->label('Municipality')
                        ->translateLabel()
                        ->live()
                        ->required(),
                    Select::make('municipality_branch_id')
                        ->options(function (callable $get) {
                            $municipalityId = $get('municipality_id');

                            if (! $municipalityId) {
                                return [];
                            }

                            return MunicipalityBranch::where('status', 'active')
                                ->where('municipality_id', $municipalityId)
                                ->pluck('name', 'id');
                        })
                        ->label('Municipality Branch')
                        ->translateLabel()
                        ->required()
                        ->live(),
                ])->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('municipality_branch.name')
                    ->label('Municipality Branch')
                    ->translateLabel(),
                TextColumn::make('name')
                    ->label('Name')
                    ->translateLabel()
                    ->searchable(),
                TextColumn::make('phone')
                    ->label('Phone')
                    ->translateLabel()
                    ->searchable(),
                TextColumn::make('email')
                    ->label('Email')
                    ->translateLabel()
                    ->searchable(),
                TextColumn::make('type')
                    ->label('Type')
                    ->translateLabel()
                    ->formatStateUsing(fn (string $state): string => __($state))
                    ->searchable(),
                TextColumn::make('status')
                    ->label('Status')
                    ->translateLabel()
                    ->badge()
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'active' => __('Active'),
                        'inactive' => __('Inactive'),
                    })
                    ->color(fn (string $state): string => match ($state) {
                        'active' => 'success',
                        'inactive' => 'danger',
                        default => 'gray',
                    })
                    ->searchable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                //
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListResidents::route('/'),
            'create' => Pages\CreateResident::route('/create'),
            'edit' => Pages\EditResident::route('/{record}/edit'),
        ];
    }

    public static function getPluralModelLabel(): string
    {
        return __('Residents/Citizens');
    }

    public static function getLabel(): string
    {
        return __('Resident/Citizen');
    }
}
