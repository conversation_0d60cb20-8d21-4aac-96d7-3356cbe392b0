<?php

declare(strict_types=1);

namespace App\Filament\Admin\Resources\InstitutionBranchResource\Pages;

use App\Filament\Admin\Resources\InstitutionBranchResource;
use Filament\Resources\Pages\CreateRecord;

class CreateInstitutionBranch extends CreateRecord
{
    protected static string $resource = InstitutionBranchResource::class;

    public function getTitle(): string
    {
        return __('Create Institution Branch');
    }
}
