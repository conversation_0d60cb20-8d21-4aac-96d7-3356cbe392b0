<?php

declare(strict_types=1);

namespace App\Filament\Admin\Resources\InstitutionBranchResource\Pages;

use App\Filament\Admin\Resources\InstitutionBranchResource;
use Filament\Actions\DeleteAction;
use Filament\Resources\Pages\EditRecord;

class EditInstitutionBranch extends EditRecord
{
    protected static string $resource = InstitutionBranchResource::class;

    public static function getNavigationLabel(): string
    {
        return __('Edit');
    }

    public function getTitle(): string
    {
        return __('Edit');
    }

    protected function getHeaderActions(): array
    {
        return [
            DeleteAction::make(),
        ];
    }
}
