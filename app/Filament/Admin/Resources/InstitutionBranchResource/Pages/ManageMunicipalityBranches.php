<?php

declare(strict_types=1);

namespace App\Filament\Admin\Resources\InstitutionBranchResource\Pages;

use App\Filament\Admin\Resources\InstitutionBranchResource;
use App\Models\InstitutionBranch;
use App\Models\MunicipalityBranch;
use Exception;
use Filament\Forms\Components\Select;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ManageRelatedRecords;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Support\Facades\Log;

class ManageMunicipalityBranches extends ManageRelatedRecords
{
    protected static string $resource = InstitutionBranchResource::class;

    protected static string $relationship = 'municipality_branches';

    protected static ?string $navigationIcon = 'heroicon-o-users';

    public static function getNavigationLabel(): string
    {
        return __('Municipality Branches');
    }

    public function getTitle(): string
    {
        /** @var InstitutionBranch $record */
        $record = $this->getRecord();

        return $record->institution->name.' - '.$record->name;
    }

    public function table(Table $table): Table
    {
        /** @var InstitutionBranch $record */
        $record = $this->getRecord();

        return $table
            ->columns([
                TextColumn::make('name')
                    ->label('Name')
                    ->translateLabel()
                    ->searchable()
                    ->sortable(),
                TextColumn::make('email')
                    ->label('Email')
                    ->translateLabel()
                    ->searchable(),
            ])
            ->headerActions([
                Tables\Actions\AttachAction::make()
                    ->form([
                        Select::make('municipality_branches')
                            ->label('Municipality Branch')
                            ->options(MunicipalityBranch::where('municipality_id', $record->institution->municipality->id)->where('status', 'active')->pluck('name', 'id'))
                            ->searchable()
                            ->required(),
                    ])
                    ->action(function (array $data): void {
                        try {
                            $municipalityBranch = MunicipalityBranch::findOrFail($data['municipality_branches']);

                            $this->record->municipality_branches()->attach($municipalityBranch);
                        } catch (Exception $e) {
                            Log::error($e->getMessage());
                            Notification::make()
                                ->title(__('Error'))
                                ->body(__('An error occurred while attaching the user.'))
                                ->danger()
                                ->send();
                        }
                    }),
            ])
            ->actions([
                Tables\Actions\DetachAction::make(),
            ]);
    }
}
