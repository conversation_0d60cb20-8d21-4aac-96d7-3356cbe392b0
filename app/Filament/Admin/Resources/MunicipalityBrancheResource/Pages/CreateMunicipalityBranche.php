<?php

declare(strict_types=1);

namespace App\Filament\Admin\Resources\MunicipalityBrancheResource\Pages;

use App\Filament\Admin\Resources\MunicipalityBranchResource;
use Filament\Resources\Pages\CreateRecord;

class CreateMunicipalityBranche extends CreateRecord
{
    protected static string $resource = MunicipalityBranchResource::class;

    public function getTitle(): string
    {
        return __('Create Municipality Branch');
    }
}
