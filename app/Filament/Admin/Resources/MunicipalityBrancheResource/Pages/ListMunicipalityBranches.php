<?php

declare(strict_types=1);

namespace App\Filament\Admin\Resources\MunicipalityBrancheResource\Pages;

use App\Filament\Admin\Resources\MunicipalityBranchResource;
use Filament\Actions\CreateAction;
use Filament\Resources\Pages\ListRecords;

class ListMunicipalityBranches extends ListRecords
{
    protected static string $resource = MunicipalityBranchResource::class;

    protected function getHeaderActions(): array
    {
        return [
            CreateAction::make()->label('Create Municipality Branch')->translateLabel(),
        ];
    }
}
