<?php

declare(strict_types=1);

namespace App\Filament\Admin\Resources\MunicipalityBrancheResource\Pages;

use App\Filament\Admin\Resources\MunicipalityBranchResource;
use Filament\Actions\DeleteAction;
use Filament\Resources\Pages\EditRecord;

class EditMunicipalityBranche extends EditRecord
{
    protected static string $resource = MunicipalityBranchResource::class;

    public static function getNavigationLabel(): string
    {
        return __('Edit Municipality Branch');
    }

    public function getTitle(): string
    {
        return __('Edit Municipality Branch');
    }

    protected function getHeaderActions(): array
    {
        return [
            DeleteAction::make(),
        ];
    }
}
