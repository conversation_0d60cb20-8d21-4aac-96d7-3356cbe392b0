<?php

declare(strict_types=1);

namespace App\Filament\Admin\Resources\MunicipalityBrancheResource\Pages;

use App\Filament\Admin\Resources\MunicipalityBranchResource;
use App\Models\User;
use Exception;
use Filament\Forms\Components\Select;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ManageRelatedRecords;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Support\Facades\Log;

class ManageUsers extends ManageRelatedRecords
{
    protected static string $resource = MunicipalityBranchResource::class;

    protected static string $relationship = 'users';

    protected static ?string $navigationIcon = 'heroicon-o-users';

    public static function getNavigationLabel(): string
    {
        return __('Users');
    }

    public function getTitle(): string
    {
        return __('Users');
    }

    public function table(Table $table): Table
    {
        return $table
            ->emptyStateHeading(__('No users found.'))
            ->emptyStateDescription(__('You can create a new user by clicking the button.'))
            ->columns([
                TextColumn::make('name')
                    ->label('Name')
                    ->translateLabel()
                    ->searchable()
                    ->sortable(),
                TextColumn::make('email')
                    ->label('Email')
                    ->translateLabel()
                    ->searchable(),
            ])
            ->headerActions([
                Tables\Actions\AttachAction::make()
                    ->form([
                        Select::make('users')
                            ->label('Users')
                            ->options(User::pluck('name', 'id'))
                            ->searchable()
                            ->required(),
                    ])
                    ->action(function (array $data): void {
                        try {
                            $user = User::findOrFail($data['users']);

                            $this->record->users()->attach($user);
                        } catch (Exception $e) {
                            Log::error($e->getMessage());
                            Notification::make()
                                ->title(__('Error'))
                                ->body(__('An error occurred while attaching the user.'))
                                ->danger()
                                ->send();
                        }
                    }),
            ])
            ->actions([
                Tables\Actions\Action::make('Detach')
                    ->label('Detach')
                    ->translateLabel()
                    ->color('danger')
                    ->icon('heroicon-o-trash')
                    ->requiresConfirmation()
                    ->action(function (User $record): void {
                        try {
                            $this->record->users()->detach($record);
                        } catch (Exception $e) {
                            Log::error($e->getMessage());
                            Notification::make()
                                ->title('Error')
                                ->body('An error occurred while detaching the user.')
                                ->danger()
                                ->send();
                        }

                    }),
            ]);
    }
}
