<?php

declare(strict_types=1);

namespace App\Filament\Admin\Widgets;

use App\Models\Institution;
use App\Models\Municipality;
use App\Models\User;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class TenantStatsOverview extends BaseWidget
{
    protected function getStats(): array
    {
        return [
            Stat::make('Total Municipalities', Municipality::count())
                ->description('Number of municipalities')
                ->icon('heroicon-o-building-office')
                ->color('primary'),

            Stat::make('Total Institutions', Institution::count())
                ->description('Number of institutions')
                ->icon('heroicon-o-academic-cap')
                ->color('warning'),

            Stat::make('Total Users', User::count())
                ->description('Number of users')
                ->icon('heroicon-o-users')
                ->color('success'),
        ];
    }
}
