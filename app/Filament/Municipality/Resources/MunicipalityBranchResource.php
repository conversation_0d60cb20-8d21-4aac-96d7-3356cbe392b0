<?php

declare(strict_types=1);

namespace App\Filament\Municipality\Resources;

use App\Filament\Municipality\Resources\MunicipalityBranchResource\Pages;
use App\Models\MunicipalityBranch;
use Dotswan\MapPicker\Fields\Map;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Pages\Page;
use Filament\Pages\SubNavigationPosition;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class MunicipalityBranchResource extends Resource
{
    protected static ?string $model = MunicipalityBranch::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static SubNavigationPosition $subNavigationPosition = SubNavigationPosition::Top;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Grid::make()
                    ->columnSpan(2)
                    ->schema([
                        Section::make(__('Municipality Branch Information'))
                            ->columns(1)
                            ->schema([
                                TextInput::make('name')
                                    ->label('Name')
                                    ->translateLabel()
                                    ->required()
                                    ->maxLength(255),
                                TextInput::make('phone')
                                    ->label('Phone')
                                    ->translateLabel()
                                    ->maxLength(255),
                                TextInput::make('email')
                                    ->label('Email')
                                    ->translateLabel()
                                    ->email()
                                    ->maxLength(255),
                            ]),
                    ]),
                Grid::make()
                    ->columnSpan(1)
                    ->columns(1)
                    ->schema([
                        Section::make()
                            ->columns(1)
                            ->schema([
                                SpatieMediaLibraryFileUpload::make('Logo')
                                    ->label('Logo')
                                    ->translateLabel()
                                    ->disk('s3')
                                    ->previewable()
                                    ->downloadable()
                                    ->acceptedFileTypes(['image/*'])
                                    ->visibility('private')
                                    ->collection('municipality_branch_logo'),
                                Map::make('location')
                                    ->label('Location')
                                    ->translateLabel()
                                    ->defaultLocation(latitude: 32.3152506, longitude: 15.0148311)
                                    ->showMarker(true)
                                    ->clickable(true)
                                    ->tilesUrl('https://tile.openstreetmap.de/{z}/{x}/{y}.png')
                                    ->zoom(6),
                                Select::make('status')
                                    ->label('Status')
                                    ->translateLabel()
                                    ->options([
                                        'active' => __('Active'),
                                        'inactive' => __('Inactive'),
                                    ])
                                    ->default('active')
                                    ->required(),
                            ]),
                    ]),
            ])->columns(3);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('Municipality Branch')
                    ->translateLabel()
                    ->searchable(),
                Tables\Columns\TextColumn::make('phone')
                    ->label('Phone')
                    ->translateLabel()
                    ->searchable(),
                Tables\Columns\TextColumn::make('email')
                    ->label('Email')
                    ->translateLabel()
                    ->searchable(),
                Tables\Columns\TextColumn::make('status')
                    ->label('Status')
                    ->badge()
                    ->formatStateUsing(fn (string $state) => __($state))
                    ->color(fn (string $state): string => match ($state) {
                        'active' => 'success',
                        'inactive' => 'danger',
                        default => 'gray',
                    })
                    ->translateLabel()
                    ->searchable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListMunicipalityBranches::route('/'),
            'create' => Pages\CreateMunicipalityBranch::route('/create'),
            'view' => Pages\ViewMunicipalityBranch::route('/{record}'),
            'edit' => Pages\EditMunicipalityBranch::route('/{record}/edit'),
            'users' => Pages\ManageUsers::route('/{record}/users'),
        ];
    }

    public static function getRecordSubNavigation(Page $page): array
    {
        return $page->generateNavigationItems([
            Pages\EditMunicipalityBranch::class,
            Pages\ManageUsers::class,
        ]);
    }

    public static function getPluralModelLabel(): string
    {
        return __('Municipality Branches');
    }

    public static function getNavigationLabel(): string
    {
        return __('Municipality Branches');
    }

    public function getTitle(): string
    {
        return __('Municipality Branches');
    }
}
