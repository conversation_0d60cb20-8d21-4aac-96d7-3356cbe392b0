<?php

declare(strict_types=1);

namespace App\Filament\Municipality\Resources\MunicipalityBranchResource\Pages;

use App\Filament\Municipality\Resources\MunicipalityBranchResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewMunicipalityBranch extends ViewRecord
{
    protected static string $resource = MunicipalityBranchResource::class;

    public static function getNavigationLabel(): string
    {
        return __('Municipality Branch');
    }

    public function getTitle(): string
    {
        return __('Municipality Branch');
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }
}
