<?php

declare(strict_types=1);

namespace App\Filament\Municipality\Resources\MunicipalityBranchResource\Pages;

use App\Filament\Municipality\Resources\MunicipalityBranchResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListMunicipalityBranches extends ListRecords
{
    protected static string $resource = MunicipalityBranchResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->label('Create Municipality Branch')
                ->translateLabel(),
        ];
    }
}
