<?php

declare(strict_types=1);

namespace App\Filament\Municipality\Resources\MunicipalityBranchResource\Pages;

use App\Filament\Municipality\Resources\MunicipalityBranchResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditMunicipalityBranch extends EditRecord
{
    protected static string $resource = MunicipalityBranchResource::class;

    public static function getNavigationLabel(): string
    {
        return __('Edit Municipality Branch');
    }

    public function getTitle(): string
    {
        return __('Edit Municipality Branch');
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }
}
