<?php

declare(strict_types=1);

namespace App\Filament\Municipality\Resources\InstitutionResource\Pages;

use App\Filament\Municipality\Resources\InstitutionResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditInstitution extends EditRecord
{
    protected static string $resource = InstitutionResource::class;

    public static function getNavigationLabel(): string
    {
        return __('Edit');
    }

    public function getTitle(): string
    {
        return __('Edit');
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
}
