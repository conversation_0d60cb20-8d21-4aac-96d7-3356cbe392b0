<?php

declare(strict_types=1);

namespace App\Filament\Municipality\Resources\InstitutionResource\Pages;

use App\Filament\Municipality\Resources\InstitutionResource;
use App\Models\InstitutionBranch;
use Dotswan\MapPicker\Fields\Map;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Pages\ManageRelatedRecords;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class ManageBranches extends ManageRelatedRecords
{
    protected static string $resource = InstitutionResource::class;

    protected static string $relationship = 'branches';

    protected static ?string $navigationIcon = 'heroicon-o-building-office';

    public static function getNavigationLabel(): string
    {
        return __('Institution Branches');
    }

    public function getTitle(): string
    {
        /** @var InstitutionBranch $record */
        $record = $this->getRecord();

        return $record->name;
    }

    public function form(Form $form): Form
    {
        return $form
            ->columns(1)
            ->schema([
                TextInput::make('name')
                    ->label('Name')
                    ->translateLabel()
                    ->required()
                    ->maxLength(255),
                TextInput::make('phone')
                    ->label('Phone')
                    ->translateLabel()
                    ->tel()
                    ->maxLength(255),
                Map::make('location')
                    ->label('Location')
                    ->translateLabel()
                    ->defaultLocation(latitude: 32.3152506, longitude: 15.0148311)
                    ->showMarker(true)
                    ->clickable(true)
                    ->tilesUrl('https://tile.openstreetmap.de/{z}/{x}/{y}.png')
                    ->zoom(12),
                Select::make('status')
                    ->label('Status')
                    ->translateLabel()
                    ->options([
                        'active' => __('Active'),
                        'inactive' => __('Inactive'),
                    ])
                    ->default('active')
                    ->required(),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->emptyStateHeading(__('No Institution Branches found.'))
            ->emptyStateDescription(__('You can create a new institution branch by clicking the button.'))
            ->columns([
                TextColumn::make('name')
                    ->label('Name')
                    ->translateLabel()
                    ->searchable()
                    ->sortable(),
                TextColumn::make('phone')
                    ->label('Phone')
                    ->translateLabel()
                    ->searchable(),
                TextColumn::make('email')
                    ->label('Email')
                    ->translateLabel()
                    ->searchable(),
                TextColumn::make('status')
                    ->label('Status')
                    ->translateLabel()
                    ->badge()
                    ->formatStateUsing(fn (string $state) => __($state))
                    ->color(fn (string $state): string => match ($state) {
                        'active' => 'success',
                        'inactive' => 'danger',
                        default => 'gray',
                    }),
            ])
            ->actions([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->label('Create Institution Branch')
                    ->translateLabel(),
            ]);
    }
}
