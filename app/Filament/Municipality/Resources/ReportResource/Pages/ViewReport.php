<?php

declare(strict_types=1);

namespace App\Filament\Municipality\Resources\ReportResource\Pages;

use App\Filament\Municipality\Resources\ReportResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewReport extends ViewRecord
{
    protected static string $resource = ReportResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }
}
