<?php

declare(strict_types=1);

namespace App\Filament\MunicipalityBranch\Resources\UserResource\Pages;

use App\Filament\MunicipalityBranch\Resources\UserResource;
use Filament\Resources\Pages\ListRecords;

class ListUsers extends ListRecords
{
    protected static string $resource = UserResource::class;

    protected function getHeaderActions(): array
    {
        return [
            //
        ];
    }
}
