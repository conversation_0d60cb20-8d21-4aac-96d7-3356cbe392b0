<?php

declare(strict_types=1);

namespace App\Filament\MunicipalityBranch\Resources\ReportResource\Pages;

use App\Filament\MunicipalityBranch\Resources\ReportResource;
use Filament\Resources\Pages\ListRecords;

class ListReports extends ListRecords
{
    protected static string $resource = ReportResource::class;

    protected function getHeaderActions(): array
    {
        return [
            //
        ];
    }
}
