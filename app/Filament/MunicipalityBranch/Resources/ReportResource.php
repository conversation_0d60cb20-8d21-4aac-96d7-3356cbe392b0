<?php

declare(strict_types=1);

namespace App\Filament\MunicipalityBranch\Resources;

use App\Filament\MunicipalityBranch\Resources\ReportResource\Pages;
use App\Models\Report;
use Dotswan\MapPicker\Fields\Map;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class ReportResource extends Resource
{
    protected static ?string $model = Report::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->columns(3)
            ->schema([
                Grid::make()
                    ->columnSpan(2)
                    ->schema([
                        Section::make()
                            ->columns(2)
                            ->schema([
                                Select::make('resident_id')
                                    ->label('Resident/Citizen')
                                    ->translateLabel()
                                    ->relationship('resident', 'name')
                                    ->columnSpan(2)
                                    ->required(),
                                TextInput::make('title')
                                    ->label('Report Title')
                                    ->translateLabel()
                                    ->required()
                                    ->maxLength(255)
                                    ->columnSpan(2),
                                Textarea::make('content')
                                    ->label('Content')
                                    ->translateLabel()
                                    ->required()
                                    ->columnSpan(2)
                                    ->maxLength(255),
                                Select::make('institution_branch_id')
                                    ->label('Institution Branch')
                                    ->translateLabel()
                                    ->relationship('institution_branch', 'name')
                                    ->columnSpan(2)
                                    ->required(),

                            ]),
                    ]),
                Grid::make()
                    ->columnSpan(1)
                    ->schema([
                        Section::make()
                            ->columns(1)
                            ->schema([
                                Select::make('type_id')
                                    ->label('Report Type')
                                    ->translateLabel()
                                    ->relationship('type', 'name')
                                    ->required(),
                                Select::make('category_id')
                                    ->label('Report Category')
                                    ->translateLabel()
                                    ->relationship('category', 'name')
                                    ->required(),
                            ]),
                        Section::make()
                            ->columns(1)
                            ->schema([
                                Map::make('location')
                                    ->label('Location')
                                    ->translateLabel()
                                    ->defaultLocation(latitude: 32.3152506, longitude: 15.0148311)
                                    ->showMarker(true)
                                    ->clickable(true)
                                    ->tilesUrl('https://tile.openstreetmap.de/{z}/{x}/{y}.png')
                                    ->zoom(14),
                            ]),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('title')
                    ->label('Report Title')
                    ->translateLabel()
                    ->searchable(),
                TextColumn::make('resident.name')
                    ->label('Resident/Citizen')
                    ->translateLabel(),
                TextColumn::make('institution_branch.institution.name')
                    ->label('Institution')
                    ->translateLabel(),
                TextColumn::make('type.name')
                    ->label('Report Type')
                    ->translateLabel(),
                TextColumn::make('category.name')
                    ->label('Report Category')
                    ->translateLabel(),
            ])
            ->filters([

            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                //
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListReports::route('/'),
            'create' => Pages\CreateReport::route('/create'),
            'view' => Pages\ViewReport::route('/{record}'),
            'edit' => Pages\EditReport::route('/{record}/edit'),
        ];
    }

    public static function getPluralModelLabel(): string
    {
        return __('Reports');
    }

    public static function getNavigationLabel(): string
    {
        return __('Reports');
    }

    public function getTitle(): string
    {
        return __('Reports');
    }
}
