<?php

declare(strict_types=1);

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ReportResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $report = $this->resource;

        return [
            'id' => $report->id,
            'title' => $report->title,
            'content' => $report->content,
            'location' => $report->location,
            'type' => $report->type->name,
            'category' => $report->category->name,
            'institution_branch' => $report->institution_branch->name,
        ];
    }
}
