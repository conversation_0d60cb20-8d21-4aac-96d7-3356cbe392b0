<?php

declare(strict_types=1);

namespace App\Http\Resources;

use App\Models\Resident;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ResidentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        /** @var Resident $resident */
        $resident = $this->resource;

        return [
            'id' => $resident->id,
            'name' => $resident->name,
            'phone' => $resident->phone,
            'email' => $resident->email,
            'type' => $resident->type,
            'status' => $resident->status,
            'municipality' => $resident->municipality->name,
            'municipality_branch' => $resident->municipality_branch->name,
        ];
    }
}
