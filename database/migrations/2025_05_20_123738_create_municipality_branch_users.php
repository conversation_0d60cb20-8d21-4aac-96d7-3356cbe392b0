<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('municipality_branch_user', function (Blueprint $table): void {
            $table->foreignUuid('municipality_branch_id')->references('id')->on('municipality_branches');
            $table->foreignUuid('user_id')->references('id')->on('users');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('municipality_branch_user');
    }
};
