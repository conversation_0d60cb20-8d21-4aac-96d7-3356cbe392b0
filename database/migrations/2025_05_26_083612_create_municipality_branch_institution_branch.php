<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('municipality_branch_institution_branch', function (Blueprint $table): void {
            $table->foreignUuid('municipality_branch_id');
            $table->foreignUuid('institution_branch_id');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('municipality_branch_institution_branch');
    }
};
