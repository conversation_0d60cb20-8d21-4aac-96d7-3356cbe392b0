<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('reports', function (Blueprint $table): void {
            $table->uuid('id')->primary();
            $table->string('title');
            $table->string('location')->nullable();
            $table->string('content');
            $table->foreignUuid('municipality_branch_id')->references('id')->on('municipality_branches');
            $table->foreignUuid('resident_id')->references('id')->on('residents');
            $table->foreignUuid('institution_branch_id')->references('id')->on('institution_branches');
            $table->foreignUuid('type_id')->references('id')->on('report_types');
            $table->foreignUuid('category_id')->references('id')->on('report_categories');
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('reports');
    }
};
