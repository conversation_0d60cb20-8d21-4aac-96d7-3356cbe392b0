<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\InstitutionBranch;
use App\Models\Report;
use App\Models\ReportCategory;
use App\Models\ReportType;
use App\Models\Resident;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;

/** @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Report> */
class ReportFactory extends Factory
{
    protected $model = Report::class;

    public function definition(): array
    {
        return [
            'title' => $this->faker->word(),
            'location' => $this->faker->word(),
            'content' => $this->faker->word(),
            'resident_id' => Resident::factory(),
            'institution_branch_id' => InstitutionBranch::factory(),
            'type_id' => ReportType::factory(),
            'category_id' => ReportCategory::factory(),
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ];
    }
}
