<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\FAQ;
use Illuminate\Database\Eloquent\Factories\Factory;

/** @extends Factory<FAQFactory> */
class FAQFactory extends Factory
{
    protected $model = FAQ::class;

    public function definition(): array
    {
        return [
            'question' => $this->faker->sentence(),
            'answer' => $this->faker->paragraph(),
            'is_active' => $this->faker->boolean(),
        ];
    }
}
