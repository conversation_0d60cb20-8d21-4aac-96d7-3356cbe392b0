<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\MunicipalityBranch;
use App\Models\Resident;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Hash;

/** @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Resident> */
class ResidentFactory extends Factory
{
    protected $model = Resident::class;

    public function definition(): array
    {
        return [
            'name' => $this->faker->name(),
            'phone' => $this->faker->phoneNumber(),
            'email' => $this->faker->unique()->safeEmail(),
            'password' => Hash::make('password'),
            //            'type' => $this->faker->randomElement(['resident', 'citizen']),
            'status' => $this->faker->randomElement(['active', 'inactive']),
            'municipality_branch_id' => MunicipalityBranch::factory(),
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ];
    }
}
