<?php

declare(strict_types=1);

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // User::factory(10)->create();

        User::factory()->create([
            'name' => 'super admin',
            'email' => '<EMAIL>',
        ]);

        User::factory()->create([
            'name' => '<PERSON><PERSON>',
            'email' => '<EMAIL>',
        ]);

        $this->call([
            DataSeeder::class,
            ReportSeeder::class,
        ]);
    }
}
