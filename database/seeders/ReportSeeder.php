<?php

declare(strict_types=1);

namespace Database\Seeders;

use App\Models\Institution;
use App\Models\InstitutionBranch;
use App\Models\MunicipalityBranch;
use App\Models\Report;
use App\Models\ReportCategory;
use App\Models\ReportType;
use App\Models\Resident;
use Illuminate\Database\Seeder;

class ReportSeeder extends Seeder
{
    public function run(): void
    {
        ReportCategory::factory()->create(['name' => 'الشكاوى', 'status' => 'active', 'institution_id' => Institution::first()->id]);
        ReportType::factory()->create(['name' => 'الشكاوى', 'status' => 'active']);

        $municipalityBranch_zaroq = MunicipalityBranch::where('name', '=', 'فرع الزروق')->get();
        $municipalityBranch_ramla = MunicipalityBranch::where('name', '=', 'فرع الرملة')->get();

        $institutionBranch_zaroq = InstitutionBranch::where('name', '=', 'فرع الزروق')->get();
        $institutionBranch_ramla = InstitutionBranch::where('name', '=', 'فرع الرملة')->get();

        Report::factory()->create([
            'title' => 'الشكاوى',
            'content' => 'الشكاوى',
            'municipality_branch_id' => $municipalityBranch_zaroq->first()->id,
            'institution_branch_id' => $institutionBranch_zaroq->first()->id,
            'resident_id' => Resident::first()->id,
            'type_id' => ReportType::first()->id,
            'category_id' => ReportCategory::first()->id,
        ]);

        Report::factory()->create([
            'title' => 'الشكاوى',
            'content' => 'الشكاوى',
            'municipality_branch_id' => $municipalityBranch_ramla->first()->id,
            'institution_branch_id' => $institutionBranch_ramla->first()->id,
            'resident_id' => Resident::first()->id,
            'type_id' => ReportType::first()->id,
            'category_id' => ReportCategory::first()->id,
        ]);

        Report::factory()->create([
            'title' => 'الشكاوى',
            'content' => 'الشكاوى',
            'municipality_branch_id' => $municipalityBranch_ramla->first()->id,
            'institution_branch_id' => $institutionBranch_ramla->first()->id,
            'resident_id' => Resident::first()->id,
            'type_id' => ReportType::first()->id,
            'category_id' => ReportCategory::first()->id,
        ]);

        Report::factory()->create([
            'title' => 'الشكاوى',
            'content' => 'الشكاوى',
            'municipality_branch_id' => $municipalityBranch_zaroq->first()->id,
            'institution_branch_id' => InstitutionBranch::latest('id')->first()->id,
            'resident_id' => Resident::first()->id,
            'type_id' => ReportType::first()->id,
            'category_id' => ReportCategory::first()->id,
        ]);
    }
}
